#!/usr/bin/env python3
"""
Morph Cloud Performance Benchmark

This script measures the performance of key Morph Cloud operations:
- VM snapshot creation speed
- Instance pause/resume speed
- Instance startup from snapshots

Demonstrates the sub-second performance that makes Infinibranch technology
revolutionary for AI agent development.
"""

import time
import statistics
from morphcloud.api import MorphCloudClient
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

class MorphBenchmark:
    def __init__(self):
        if not os.getenv('MORPH_API_KEY'):
            raise ValueError("MORPH_API_KEY must be set in .env file or environment")
        
        self.client = MorphCloudClient()
        self.results = {
            'snapshot_times': [],
            'pause_times': [],
            'resume_times': [],
            'startup_times': [],
            'instance_creation_times': []
        }
    
    def time_operation(self, operation_name, operation_func, *args, **kwargs):
        """Time a single operation and return the duration in milliseconds"""
        print(f"  ⏱️  Timing {operation_name}...", end=" ", flush=True)
        start_time = time.time()
        
        try:
            result = operation_func(*args, **kwargs)
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            print(f"{duration_ms:.1f}ms")
            return duration_ms, result
        except Exception as e:
            print(f"FAILED: {e}")
            return None, None
    
    def create_test_instance(self):
        """Create a test instance with some workload"""
        print("\n🏗️  Creating test instance...")
        
        # Create initial snapshot
        duration, snapshot = self.time_operation(
            "initial snapshot creation",
            self.client.snapshots.create,
            image_id="morphvm-minimal",
            vcpus=1,
            memory=128,
            disk_size=700
        )
        
        if not snapshot:
            raise Exception("Failed to create initial snapshot")
        
        # Start instance
        duration, instance = self.time_operation(
            "instance startup",
            self.client.instances.start,
            snapshot.id
        )
        
        if not instance:
            raise Exception("Failed to start instance")
        
        self.results['instance_creation_times'].append(duration)
        
        # Create some workload in the instance
        print("  📝 Setting up workload...")
        
        # Create a simple background process
        workload_script = """#!/bin/bash
# Create some files and processes to make the snapshot more realistic
mkdir -p /tmp/workload
for i in {1..100}; do
    echo "Data file $i: $(date)" > /tmp/workload/file_$i.txt
done

# Start a background counter
count=1
while [ $count -le 1000 ]; do
    echo $count > /tmp/workload/counter.txt
    sleep 0.1
    count=$((count + 1))
done &

# Create some memory usage
python3 -c "
import time
data = ['x' * 1000 for _ in range(1000)]
while True:
    time.sleep(0.1)
" &
"""
        
        instance.exec(f"cat > /tmp/setup_workload.sh << 'EOF'\n{workload_script}\nEOF")
        instance.exec("chmod +x /tmp/setup_workload.sh")
        instance.exec("nohup /tmp/setup_workload.sh > /dev/null 2>&1 &")
        
        # Wait for workload to start
        time.sleep(2)
        print("  ✅ Workload setup complete")
        
        return instance
    
    def benchmark_snapshots(self, instance, num_tests=5):
        """Benchmark snapshot creation speed"""
        print(f"\n📸 Benchmarking snapshot creation ({num_tests} tests)...")
        
        for i in range(num_tests):
            print(f"  Test {i+1}/{num_tests}:")
            duration, snapshot = self.time_operation(
                f"snapshot creation #{i+1}",
                instance.snapshot
            )
            
            if duration:
                self.results['snapshot_times'].append(duration)
            
            # Clean up snapshot to avoid accumulation
            if snapshot:
                try:
                    snapshot.delete()
                except:
                    pass  # Ignore cleanup errors
    
    def benchmark_pause_resume(self, instance, num_tests=3):
        """Benchmark pause and resume operations"""
        print(f"\n⏸️  Benchmarking pause/resume operations ({num_tests} tests)...")
        
        for i in range(num_tests):
            print(f"  Test {i+1}/{num_tests}:")
            
            # Pause
            duration, _ = self.time_operation(
                f"pause #{i+1}",
                instance.pause
            )
            if duration:
                self.results['pause_times'].append(duration)
            
            # Wait a moment
            time.sleep(1)
            
            # Resume
            duration, _ = self.time_operation(
                f"resume #{i+1}",
                instance.resume
            )
            if duration:
                self.results['resume_times'].append(duration)
            
            # Wait before next test
            time.sleep(1)
    
    def benchmark_startup_from_snapshot(self, instance, num_tests=21):
        """Benchmark starting new instances from snapshots"""
        print(f"\n🚀 Benchmarking startup from snapshot ({num_tests} tests)...")
        
        # Create a snapshot to use for startup tests
        print("  📸 Creating snapshot for startup tests...")
        snapshot = instance.snapshot()
        
        instances_to_cleanup = []
        
        for i in range(num_tests):
            print(f"  Test {i+1}/{num_tests}:")
            duration, new_instance = self.time_operation(
                f"startup from snapshot #{i+1}",
                self.client.instances.start,
                snapshot.id
            )
            
            if duration:
                self.results['startup_times'].append(duration)
            
            if new_instance:
                instances_to_cleanup.append(new_instance)
        
        # Cleanup
        print("  🧹 Cleaning up test instances...")
        for inst in instances_to_cleanup:
            try:
                inst.stop()
            except:
                pass
        
        try:
            snapshot.delete()
        except:
            pass
    
    def print_results(self):
        """Print benchmark results with statistics"""
        print("\n" + "="*60)
        print("🏆 MORPH CLOUD PERFORMANCE BENCHMARK RESULTS")
        print("="*60)
        
        def print_stats(operation_name, times):
            if not times:
                print(f"\n{operation_name}: No data collected")
                return
            
            avg = statistics.mean(times)
            median = statistics.median(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"\n{operation_name}:")
            print(f"  📊 Average: {avg:.1f}ms")
            print(f"  📈 Median:  {median:.1f}ms")
            print(f"  ⚡ Fastest: {min_time:.1f}ms")
            print(f"  🐌 Slowest: {max_time:.1f}ms")
            print(f"  📋 Samples: {len(times)}")
        
        print_stats("🚀 Instance Creation", self.results['instance_creation_times'])
        print_stats("📸 Snapshot Creation", self.results['snapshot_times'])
        print_stats("⏸️  Instance Pause", self.results['pause_times'])
        print_stats("▶️  Instance Resume", self.results['resume_times'])
        print_stats("🏃 Startup from Snapshot", self.results['startup_times'])
        
        # Summary insights
        print("\n" + "="*60)
        print("💡 PERFORMANCE INSIGHTS")
        print("="*60)
        
        if self.results['snapshot_times']:
            avg_snapshot = statistics.mean(self.results['snapshot_times'])
            print(f"• Snapshots average {avg_snapshot:.1f}ms - that's {avg_snapshot/1000:.2f} seconds!")
        
        if self.results['startup_times']:
            avg_startup = statistics.mean(self.results['startup_times'])
            print(f"• Instance startup averages {avg_startup:.1f}ms vs traditional VMs (120-180 seconds)")
            speedup = 150000 / avg_startup  # Assuming 2.5 min traditional startup
            print(f"• That's approximately {speedup:.0f}x faster than traditional VMs!")
        
        if self.results['pause_times'] and self.results['resume_times']:
            avg_pause = statistics.mean(self.results['pause_times'])
            avg_resume = statistics.mean(self.results['resume_times'])
            print(f"• Pause/resume cycle: {avg_pause + avg_resume:.1f}ms total")
        
        print("\n🌟 This is the power of Infinibranch technology!")
        print("   Perfect for AI agents that need instant state management.")

def main():
    print("🚀 Morph Cloud Performance Benchmark")
    print("====================================")
    print("This benchmark measures the speed of key Morph Cloud operations")
    print("to demonstrate the performance advantages of Infinibranch technology.\n")
    
    try:
        benchmark = MorphBenchmark()
        
        # Create test instance
        instance = benchmark.create_test_instance()
        
        # Run benchmarks
        benchmark.benchmark_snapshots(instance, num_tests=21)
        benchmark.benchmark_pause_resume(instance, num_tests=3)
        benchmark.benchmark_startup_from_snapshot(instance, num_tests=3)
        
        # Print results
        benchmark.print_results()
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        try:
            instance.stop()
            print("✅ Cleanup complete")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")
    
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure MORPH_API_KEY is set in your .env file")
        print("2. Check your internet connection")
        print("3. Verify you have credits in your Morph Cloud account")

if __name__ == "__main__":
    main()
